import { supabase } from '@/integrations/supabase/client';
import { Fu<PERSON><PERSON>atch<PERSON>, FuzzyMatchResult } from '@/services/matching/FuzzyMatcher';

/**
 * Medicine database record interface
 */
export interface Medicine {
  id: string;
  generic_name: string;
  brand_names: string[];
  alternative_names: string[];
  medicine_type: string;
  therapeutic_class?: string;
  pharmacological_class?: string;
  strength?: string;
  dosage_forms: string[];
  route_of_administration: string[];
  indications: string[];
  contraindications: string[];
  side_effects: string[];
  warnings: string[];
  interactions: string[];
  typical_dosage?: string;
  frequency?: string;
  duration?: string;
  special_instructions?: string;
  fda_approved: boolean;
  prescription_required: boolean;
  over_the_counter: boolean;
  popularity_score: number;
  created_at: string;
  updated_at: string;
}

/**
 * Medicine search result interface
 */
export interface MedicineSearchResult {
  id: string;
  generic_name: string;
  brand_names: string[];
  medicine_type: string;
  strength?: string;
  indications: string[];
  similarity_score: number;
  search_rank: number;
}

/**
 * Medicine lookup result interface
 */
export interface MedicineLookupResult {
  success: boolean;
  medicine?: Medicine;
  confidence: number;
  suggestions?: MedicineSearchResult[];
  message?: string;
  source?: 'exact_match' | 'fuzzy_match' | 'database' | 'api' | 'fallback';
  matched_term?: string;
  fuzzy_details?: FuzzyMatchResult;
}

/**
 * Service for interacting with the medicines database
 */
export class MedicineDatabaseService {
  /**
   * Search for medicines using fuzzy matching and full-text search
   */
  static async searchMedicines(
    searchTerm: string,
    limit: number = 10,
    similarityThreshold: number = 0.3
  ): Promise<MedicineSearchResult[]> {
    try {
      console.log(`🔍 Searching medicines for: "${searchTerm}"`);
      
      const { data, error } = await supabase.rpc('search_medicines', {
        search_term: searchTerm.toLowerCase().trim(),
        limit_count: limit,
        similarity_threshold: similarityThreshold
      });

      if (error) {
        console.error('❌ Medicine search error:', error);
        return [];
      }

      console.log(`✅ Found ${data?.length || 0} medicine matches`);
      return data || [];
    } catch (error) {
      console.error('❌ Medicine search failed:', error);
      return [];
    }
  }

  /**
   * Get detailed medicine information by ID
   */
  static async getMedicineById(id: string): Promise<Medicine | null> {
    try {
      const { data, error } = await supabase
        .from('medicines')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('❌ Medicine lookup error:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('❌ Medicine lookup failed:', error);
      return null;
    }
  }

  /**
   * Comprehensive medicine lookup with confidence scoring
   */
  static async lookupMedicine(medicineName: string): Promise<MedicineLookupResult> {
    try {
      console.log(`🔍 Looking up medicine: "${medicineName}"`);
      
      // Clean and normalize the input
      const cleanName = medicineName.toLowerCase().trim();
      
      if (cleanName.length < 2) {
        return {
          success: false,
          confidence: 0,
          suggestions: [],
          message: 'Medicine name too short'
        };
      }

      // Search for medicines
      const searchResults = await this.searchMedicines(cleanName, 5, 0.2);
      
      if (searchResults.length === 0) {
        return {
          success: false,
          confidence: 0,
          suggestions: [],
          message: 'No medicines found matching the search term'
        };
      }

      // Get the best match
      const bestMatch = searchResults[0];
      const confidence = this.calculateConfidence(cleanName, bestMatch);
      
      // If confidence is high enough, get full medicine details
      if (confidence >= 70) {
        const fullMedicine = await this.getMedicineById(bestMatch.id);
        
        if (fullMedicine) {
          // Update popularity score
          await this.updatePopularityScore(bestMatch.id);
          
          return {
            success: true,
            medicine: fullMedicine,
            confidence,
            suggestions: searchResults.slice(1), // Other suggestions
            message: 'Medicine identified successfully'
          };
        }
      }

      // Return suggestions if no high-confidence match
      return {
        success: false,
        confidence,
        suggestions: searchResults,
        message: confidence > 50 
          ? 'Possible matches found, please verify'
          : 'Low confidence matches found'
      };

    } catch (error) {
      console.error('❌ Medicine lookup failed:', error);
      return {
        success: false,
        confidence: 0,
        suggestions: [],
        message: 'Database lookup failed'
      };
    }
  }

  /**
   * Calculate confidence score for a medicine match
   */
  private static calculateConfidence(
    searchTerm: string,
    result: MedicineSearchResult
  ): number {
    let confidence = 0;
    
    // Base confidence from similarity score (0-50 points)
    confidence += result.similarity_score * 50;
    
    // Bonus for search rank (0-20 points)
    confidence += Math.min(result.search_rank * 20, 20);
    
    // Exact match bonus (0-30 points)
    const exactMatch = [
      result.generic_name,
      ...result.brand_names
    ].some(name => 
      name.toLowerCase() === searchTerm.toLowerCase()
    );
    
    if (exactMatch) {
      confidence += 30;
    }
    
    // Partial match bonus (0-15 points)
    const partialMatch = [
      result.generic_name,
      ...result.brand_names
    ].some(name => 
      name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      searchTerm.toLowerCase().includes(name.toLowerCase())
    );
    
    if (partialMatch && !exactMatch) {
      confidence += 15;
    }
    
    return Math.min(Math.round(confidence), 100);
  }

  /**
   * Update popularity score for a medicine
   */
  private static async updatePopularityScore(medicineId: string): Promise<void> {
    try {
      await supabase.rpc('increment_popularity', { medicine_id: medicineId });
    } catch (error) {
      console.warn('⚠️ Failed to update popularity score:', error);
    }
  }

  /**
   * Add a new medicine to the database
   */
  static async addMedicine(medicine: Partial<Medicine>): Promise<Medicine | null> {
    try {
      const { data, error } = await supabase
        .from('medicines')
        .insert([medicine])
        .select()
        .single();

      if (error) {
        console.error('❌ Failed to add medicine:', error);
        return null;
      }

      console.log('✅ Medicine added successfully:', data.generic_name);
      return data;
    } catch (error) {
      console.error('❌ Failed to add medicine:', error);
      return null;
    }
  }

  /**
   * Get popular medicines
   */
  static async getPopularMedicines(limit: number = 20): Promise<Medicine[]> {
    try {
      const { data, error } = await supabase
        .from('medicines')
        .select('*')
        .order('popularity_score', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('❌ Failed to get popular medicines:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('❌ Failed to get popular medicines:', error);
      return [];
    }
  }

  /**
   * Get medicines by type
   */
  static async getMedicinesByType(type: string, limit: number = 50): Promise<Medicine[]> {
    try {
      const { data, error } = await supabase
        .from('medicines')
        .select('*')
        .eq('medicine_type', type)
        .order('popularity_score', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('❌ Failed to get medicines by type:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('❌ Failed to get medicines by type:', error);
      return [];
    }
  }

  /**
   * Link an OCR scan to a medicine in the database
   */
  static async linkScanToMedicine(
    scanId: string,
    medicineId: string,
    confidence: 'low' | 'medium' | 'high'
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('medicine_scans')
        .update({
          medicine_id: medicineId,
          confidence_level: confidence,
          verification_status: confidence === 'high' ? 'verified' : 'unverified'
        })
        .eq('id', scanId);

      if (error) {
        console.error('❌ Failed to link scan to medicine:', error);
        return false;
      }

      console.log('✅ Scan linked to medicine successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to link scan to medicine:', error);
      return false;
    }
  }

  /**
   * Enhanced medicine lookup with fuzzy matching for OCR results
   */
  static async lookupMedicineWithFuzzyMatching(
    extractedText: string,
    potentialNames: string[]
  ): Promise<MedicineLookupResult | null> {
    if (!extractedText && (!potentialNames || potentialNames.length === 0)) {
      return null;
    }

    console.log('🔍 Starting fuzzy medicine lookup...');
    console.log('📝 Extracted text:', extractedText);
    console.log('🎯 Potential names:', potentialNames);

    try {
      // First, try exact matches with potential names
      for (const name of potentialNames) {
        const exactMatch = await this.findExactMedicine(name);
        if (exactMatch) {
          console.log('✅ Exact match found:', exactMatch.generic_name);
          return {
            medicine: exactMatch,
            confidence: 100,
            source: 'exact_match',
            matched_term: name
          };
        }
      }

      // Get all medicines for fuzzy matching
      const { data: medicines, error } = await supabase
        .from('medicines')
        .select('id, generic_name, brand_names, alternative_names, medicine_type, indications, popularity_score')
        .limit(1000);

      if (error || !medicines || medicines.length === 0) {
        console.warn('⚠️ Could not fetch medicines for fuzzy matching');
        return null;
      }

      // Create comprehensive list of medicine names for fuzzy matching
      const allMedicineNames: Array<{name: string, medicine: Medicine}> = [];

      for (const medicine of medicines) {
        // Add generic name
        allMedicineNames.push({ name: medicine.generic_name, medicine });

        // Add brand names
        if (medicine.brand_names) {
          for (const brandName of medicine.brand_names) {
            allMedicineNames.push({ name: brandName, medicine });
          }
        }

        // Add alternative names
        if (medicine.alternative_names) {
          for (const altName of medicine.alternative_names) {
            allMedicineNames.push({ name: altName, medicine });
          }
        }
      }

      const medicineNamesList = allMedicineNames.map(item => item.name);
      let bestMatch: { result: FuzzyMatchResult; medicine: Medicine } | null = null;

      // Try fuzzy matching with potential names
      for (const potentialName of potentialNames) {
        const fuzzyResult = FuzzyMatcher.findBestMatch(potentialName, medicineNamesList, {
          threshold: 0.7,
          includePartialMatches: true
        });

        if (fuzzyResult && fuzzyResult.confidence >= 0.7) {
          const matchedMedicine = allMedicineNames.find(item => item.name === fuzzyResult.match)?.medicine;
          if (matchedMedicine && (!bestMatch || fuzzyResult.confidence > bestMatch.result.confidence)) {
            bestMatch = { result: fuzzyResult, medicine: matchedMedicine };
          }
        }
      }

      // Try fuzzy matching with the entire extracted text
      if (!bestMatch && extractedText) {
        const words = extractedText.split(/\s+/).filter(word => word.length >= 4);
        for (const word of words) {
          const fuzzyResult = FuzzyMatcher.findBestMatch(word, medicineNamesList, {
            threshold: 0.75,
            includePartialMatches: true
          });

          if (fuzzyResult && fuzzyResult.confidence >= 0.75) {
            const matchedMedicine = allMedicineNames.find(item => item.name === fuzzyResult.match)?.medicine;
            if (matchedMedicine && (!bestMatch || fuzzyResult.confidence > bestMatch.result.confidence)) {
              bestMatch = { result: fuzzyResult, medicine: matchedMedicine };
            }
          }
        }
      }

      if (bestMatch) {
        console.log(`🎯 Fuzzy match found: ${bestMatch.medicine.generic_name} (${(bestMatch.result.confidence * 100).toFixed(1)}% confidence)`);

        // Get full medicine details
        const fullMedicine = await this.getMedicineById(bestMatch.medicine.id);

        return {
          medicine: fullMedicine || bestMatch.medicine,
          confidence: Math.round(bestMatch.result.confidence * 100),
          source: 'fuzzy_match',
          matched_term: bestMatch.result.match,
          fuzzy_details: bestMatch.result
        };
      }

      console.log('❌ No fuzzy matches found');
      return null;

    } catch (error) {
      console.error('❌ Fuzzy medicine lookup error:', error);
      return null;
    }
  }

  /**
   * Find exact medicine match by name
   */
  private static async findExactMedicine(name: string): Promise<Medicine | null> {
    try {
      const cleanName = name.trim().toLowerCase();

      const { data, error } = await supabase
        .from('medicines')
        .select('*')
        .or(`generic_name.ilike.${cleanName},brand_names.cs.{${cleanName}},alternative_names.cs.{${cleanName}}`)
        .limit(1)
        .single();

      if (error || !data) {
        return null;
      }

      return data as Medicine;
    } catch (error) {
      console.error('Exact medicine search error:', error);
      return null;
    }
  }
}
